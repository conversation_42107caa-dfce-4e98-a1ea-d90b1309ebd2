import 'package:easydine_main/blocs/demopos/pos_bloc.dart';
import 'package:easydine_main/blocs/demopos/pos_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import '../../screens/demo/widgets/menu_section.dart';
import '../../widgets/menu_section.dart';
import '../../utils/pos_snackbar_utils.dart';

class DemoPOSScreen extends StatelessWidget {
  const DemoPOSScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      resizeToAvoidBottomInset: false,
      body: BlocListener<DemoPOSBloc, DemoPOSState>(
        listener: (context, state) {
          if (state.error != null) {
            _showCustomSnackBar(context, state.error!);
          }
        },
        child: DemoMenuSection(),
      ),
    );
  }

  /// Show custom snackbar with reduced width to avoid overlaying place order button
  void _showCustomSnackBar(BuildContext context, String message) {
    POSSnackBarUtils.showError(context, message);
  }
}
