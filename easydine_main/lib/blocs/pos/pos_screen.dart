import 'package:easydine_main/blocs/pos/pos_bloc.dart';
import 'package:easydine_main/blocs/pos/pos_state.dart';
import 'package:easydine_main/blocs/pos/pos_event.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import '../../widgets/menu_section.dart';
import '../../utils/pos_snackbar_utils.dart';

class POSScreen extends StatefulWidget {
  const POSScreen({super.key});

  @override
  State<POSScreen> createState() => _POSScreenState();
}

class _POSScreenState extends State<POSScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize POS and load existing cart when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<POSBloc>().add(const InitializePOS());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      resizeToAvoidBottomInset: false,
      body: BlocListener<POSBloc, POSState>(
        listener: (context, state) {
          if (state.error != null) {
            _showCustomSnackBar(context, state.error!);
          }
        },
        child: MenuSection(),
      ),
    );
  }

  /// Show custom snackbar with reduced width to avoid overlaying place order button
  void _showCustomSnackBar(BuildContext context, String message) {
    POSSnackBarUtils.showError(context, message);
  }
}
