import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Utility class for showing custom snackbars in POS context
/// that don't overlay the place order button
class POSSnackBarUtils {
  /// Show a custom snackbar with reduced width for POS screens
  static void showPOSSnackBar(
    BuildContext context,
    String message, {
    Color? backgroundColor,
    IconData? icon,
    Duration? duration,
    bool isError = false,
  }) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    
    // Clear any existing snackbars
    ScaffoldMessenger.of(context).clearSnackBars();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 12),
            ],
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.dmSans(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor ?? (isError ? Colors.red : Colors.green),
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.only(
          bottom: 16,
          left: 16,
          right: isLandscape 
              ? MediaQuery.of(context).size.width * 0.35  // Leave space for cart panel
              : 16,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        duration: duration ?? const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white70,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Show success snackbar
  static void showSuccess(BuildContext context, String message) {
    showPOSSnackBar(
      context,
      message,
      backgroundColor: Colors.green,
      icon: Icons.check_circle,
    );
  }

  /// Show error snackbar
  static void showError(BuildContext context, String message) {
    showPOSSnackBar(
      context,
      message,
      backgroundColor: Colors.red,
      icon: Icons.error,
      isError: true,
    );
  }

  /// Show warning snackbar
  static void showWarning(BuildContext context, String message) {
    showPOSSnackBar(
      context,
      message,
      backgroundColor: Colors.orange,
      icon: Icons.warning,
    );
  }

  /// Show info snackbar
  static void showInfo(BuildContext context, String message) {
    showPOSSnackBar(
      context,
      message,
      backgroundColor: Colors.blue,
      icon: Icons.info,
    );
  }
}
