import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/order_model.dart';
import '../models/bill_model.dart';
import '../models/api_result.dart';
import '../utils/http_client.dart';
import '../config/env_config.dart';

/// Service for handling order-related API calls
class OrderService {
  static String get _baseUrl => EnvConfig.apiBaseUrl;

  /// Fetch all orders from the API
  /// GET {{LAMBDA_HOST}}/order/view_all
  static Future<OrdersApiResponse?> getAllOrders({
    int page = 1,
    int limit = 10,
    String? status,
    String? orderType,
    String? branchId,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (status != null) queryParams['status'] = status;
      if (orderType != null) queryParams['orderType'] = orderType;
      if (branchId != null) queryParams['branchId'] = branchId;

      final url = '$_baseUrl/order/running-order/view_all';
      debugPrint('🍽️ OrderService: Fetching orders from $url');
      debugPrint('🍽️ OrderService: Query params: $queryParams');

      final response = await HttpClientService.get(url, params: queryParams);
      debugPrint('🍽️ OrderService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('🍽️ OrderService: Successfully parsed orders response');

        return OrdersApiResponse.fromJson(jsonData);
      } else {
        debugPrint('🍽️ OrderService: HTTP error ${response.statusCode}');
        debugPrint('🍽️ OrderService: Response body: ${response.body}');
        throw Exception('Failed to fetch orders: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error fetching orders: $e');
      return null;
    }
  }

  /// Fetch orders by status
  static Future<OrdersApiResponse?> getOrdersByStatus(String status) async {
    return getAllOrders(status: status);
  }

  /// Fetch bill by table ID
  /// GET {{LAMBDA_HOST}}/order/get-bill-by-table/:tableId
  static Future<TableBillResponse?> getBillByTable(String tableId) async {
    try {
      final url = '$_baseUrl/order/get-bill-by-table/$tableId';
      debugPrint('🧾 OrderService: Fetching bill for table $tableId from $url');

      final response = await HttpClientService.get(url);
      debugPrint('🧾 OrderService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('🧾 OrderService: Successfully parsed bill response');

        return TableBillResponse.fromJson(jsonData);
      } else {
        debugPrint('🧾 OrderService: HTTP error ${response.statusCode}');
        debugPrint('🧾 OrderService: Response body: ${response.body}');
        throw Exception('Failed to fetch bill: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🧾 OrderService: Error fetching bill: $e');
      return null;
    }
  }

  /// Fetch orders by order type
  static Future<OrdersApiResponse?> getOrdersByType(String orderType) async {
    return getAllOrders(orderType: orderType);
  }

  /// Fetch orders for a specific branch
  static Future<OrdersApiResponse?> getOrdersByBranch(String branchId) async {
    return getAllOrders(branchId: branchId);
  }

  /// Fetch completed orders from the new endpoint
  /// GET {{LAMBDA_HOST}}/order/running-orders/done/view_all
  static Future<OrdersApiResponse?> getCompletedOrders({
    int page = 1,
    int limit = 50,
    String? orderType,
    String? branchId,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (orderType != null) queryParams['orderType'] = orderType;
      if (branchId != null) queryParams['branchId'] = branchId;

      final url = '$_baseUrl/order/search-orders?status=COMPLETED';
      debugPrint('🏁 OrderService: Fetching completed orders from $url');
      debugPrint('🏁 OrderService: Query params: $queryParams');

      final response = await HttpClientService.get(url, params: queryParams);
      debugPrint('🏁 OrderService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('🏁 OrderService: Successfully parsed completed orders response');

        return OrdersApiResponse.fromJson(jsonData);
      } else {
        debugPrint('🏁 OrderService: HTTP error ${response.statusCode}');
        debugPrint('🏁 OrderService: Response body: ${response.body}');
        throw Exception('Failed to fetch completed orders: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🏁 OrderService: Error fetching completed orders: $e');
      return null;
    }
  }

  /// Update order status
  /// PATCH {{LAMBDA_HOST}}/order/update-status/:orderDetailId
  static Future<bool> updateOrderStatus(
      String orderDetailId, String status) async {
    try {
      final url = '$_baseUrl/order/update-status/$orderDetailId';
      debugPrint('🍽️ OrderService: Updating order status at $url');

      final body = json.encode({'status': status});
      final response = await HttpClientService.patch(url, body: body);

      debugPrint(
          '🍽️ OrderService: Update status response: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('🍽️ OrderService: Successfully updated order status');
        return true;
      } else {
        debugPrint(
            '🍽️ OrderService: Failed to update order status: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error updating order status: $e');
      return false;
    }
  }

  /// Update queue status
  /// PATCH {{LAMBDA_QA}}/order/queue-status/:orderQueueId
  static Future<bool> updateQueueStatus(
      String orderQueueId, String status) async {
    try {
      final url = '$_baseUrl/order/queue-status/$orderQueueId';
      debugPrint('🍽️ OrderService: Updating queue status at $url');

      final body = json.encode({'status': status});
      final response = await HttpClientService.patch(url, body: body);

      debugPrint(
          '🍽️ OrderService: Update queue status response: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('🍽️ OrderService: Successfully updated queue status');
        return true;
      } else {
        debugPrint(
            '🍽️ OrderService: Failed to update queue status: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error updating queue status: $e');
      return false;
    }
  }

  /// Update order details (partial update)
  /// PATCH {{LAMBDA_HOST}}/order/update/:orderDetailId
  static Future<bool> updateOrderDetails(
      String orderDetailId, Map<String, dynamic> updateData) async {
    try {
      final url = '$_baseUrl/order/update/$orderDetailId';
      debugPrint('🍽️ OrderService: Updating order details at $url');
      debugPrint('🍽️ OrderService: Update data: ${json.encode(updateData)}');

      final response =
          await HttpClientService.patch(url, body: json.encode(updateData));

      debugPrint(
          '🍽️ OrderService: Update order response: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('🍽️ OrderService: Successfully updated order details');
        return true;
      } else {
        debugPrint(
            '🍽️ OrderService: Failed to update order details: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error updating order details: $e');
      return false;
    }
  }

  /// Add items to existing order
  /// POST {{LAMBDA_HOST}}/order/items/:orderDetailId
  static Future<bool> addItemsToOrder(
      String orderDetailId, String cartId) async {
    try {
      final url = '$_baseUrl/order/items/$orderDetailId';
      debugPrint('🍽️ OrderService: Adding items to order at $url');

      final body = json.encode({'cartId': cartId});
      debugPrint('🍽️ OrderService: Request body: $body');

      final response = await HttpClientService.post(url, body: body);

      debugPrint(
          '🍽️ OrderService: Add items response: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        debugPrint('🍽️ OrderService: Successfully added items to order');
        return true;
      } else {
        debugPrint(
            '🍽️ OrderService: Failed to add items to order: ${response.statusCode}');
        debugPrint('🍽️ OrderService: Response body: ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error adding items to order: $e');
      return false;
    }
  }

  /// Get order details by ID
  /// GET {{LAMBDA_HOST}}/order/get-order/:orderDetailId
  static Future<OrderDetail?> getOrderById(String orderDetailId) async {
    try {
      final url = '$_baseUrl/order/get-order/$orderDetailId';
      debugPrint('🍽️ OrderService: Fetching order details from $url');

      final response = await HttpClientService.get(url);
      debugPrint('🍽️ OrderService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('🍽️ OrderService: Successfully parsed order details');

        return OrderDetail.fromJson(jsonData['data']['data']);
      } else {
        debugPrint('🍽️ OrderService: HTTP error ${response.statusCode}');
        throw Exception(
            'Failed to fetch order details: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error fetching order details: $e');
      return null;
    }
  }

  /// Get order details by ID and convert to legacy format for OrderDetailsModal
  /// GET {{LAMBDA_HOST}}/order/get-order/:orderDetailId
  static Future<Map<String, dynamic>?> getOrderForDialog(String orderDetailId) async {
    try {
      final url = '$_baseUrl/order/get-order/$orderDetailId';
      debugPrint('🍽️ OrderService: Fetching order details for dialog from $url');

      final response = await HttpClientService.get(url);
      debugPrint('🍽️ OrderService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        debugPrint('🍽️ OrderService: Successfully parsed order details for dialog');

        // Parse the nested data structure
        final orderData = jsonData['data']['data'] as Map<String, dynamic>;
        final orderDetail = OrderDetail.fromJson(orderData);

        // Convert to legacy format and return
        return convertToLegacyFormat(orderDetail);
      } else {
        debugPrint('🍽️ OrderService: HTTP error ${response.statusCode}');
        throw Exception(
            'Failed to fetch order details: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error fetching order details for dialog: $e');
      return null;
    }
  }

  /// Convert OrderDetail to legacy format for backward compatibility
  static Map<String, dynamic> convertToLegacyFormat(OrderDetail orderDetail) {
    // Determine which items to use based on status
    final bool useQueueItems = orderDetail.status.toUpperCase() != 'CHECKOUT';
    final List<Map<String, dynamic>> itemsToUse;

    if (useQueueItems && orderDetail.queueItems != null && orderDetail.queueItems!.isNotEmpty) {
      // Use queue items for non-checkout orders
      itemsToUse = orderDetail.queueItems!
          .expand((queueItem) => queueItem.orderItems)
          .map((item) => {
                'id': item.orderItemId,
                'name': item.name,
                'quantity': item.quantity,
                'price': double.tryParse(item.price) ?? 0.0,
                'customization': _extractCustomization(item),
                'baseItem': {
                  'id': item.baseItem.id,
                  'name': item.baseItem.name,
                  'price': item.baseItem.price,
                },
              })
          .toList();
    } else {
      // Use order items for checkout orders or when no queue items exist
      itemsToUse = orderDetail.orderItems
          .map((item) => {
                'id': item.orderItemId,
                'name': item.name,
                'quantity': item.quantity,
                'price': double.tryParse(item.price) ?? 0.0,
                'customization': _extractCustomization(item),
                'baseItem': {
                  'id': item.baseItem.id,
                  'name': item.baseItem.name,
                  'price': item.baseItem.price,
                },
              })
          .toList();
    }

    return {
      'id': orderDetail.orderCode,
      'table': orderDetail.table?.name ??
          'Unknown Table', // Convert TableInfo to string
      'status': _mapStatusToLegacy(orderDetail.status),
      'type': orderDetail.orderType.name,
      'items': itemsToUse,
      'time': _formatTime(orderDetail.createdAt),
      'customer': orderDetail.customerInfo?.name ??
          orderDetail.orderedBy?.name ??
          'Unknown',
      'total': double.tryParse(orderDetail.total) ?? 0.0,
      'orderDetailId': orderDetail.orderDetailId,
      'orderApproval': orderDetail.orderApproval,
      'estimatedPrepTime': orderDetail.estimatedPrepTime,
      'assignedWaiter': orderDetail.assignedWaiter?.name,
      'assignedChef': orderDetail.assignedChef?.name,
      'notes': orderDetail.notes,
      'numberOfPeople': orderDetail.numberOfPeople,
      'miscItems': orderDetail.miscItems,
      'alerts': orderDetail.alerts,
      'queueItems': orderDetail.queueItems?.map((queueItem) => {
                'orderQueueId': queueItem.orderQueueId,
                'status': queueItem.status,
                'estimatedPrepTime': queueItem.estimatedPrepTime,
                'priority': queueItem.priority,
                'queuePosition': queueItem.queuePosition,
                'notes': queueItem.notes,
                'orderItems': queueItem.orderItems
                    .map((item) => {
                          'id': item.orderItemId,
                          'name': item.name,
                          'quantity': item.quantity,
                          'price': double.tryParse(item.price) ?? 0.0,
                          'customization': _extractCustomization(item),
                          'baseItem': {
                            'id': item.baseItem.id,
                            'name': item.baseItem.name,
                            'price': item.baseItem.price,
                          },
                        })
                    .toList(),
              })
          .toList() ?? [],
      'customerInfo': orderDetail.customerInfo != null
          ? {
              'name': orderDetail.customerInfo!.name,
              'phone': orderDetail.customerInfo!.phoneNumber,
              'email': orderDetail.customerInfo!.email,
            }
          : null,
      'orderedBy': orderDetail.orderedBy != null
          ? {
              'name': orderDetail.orderedBy!.name,
              'id': orderDetail.orderedBy!.id,
            }
          : null,
      'prepStartTime': orderDetail.prepStartTime != null
          ? _formatTime(orderDetail.prepStartTime!)
          : null,
      'readyTime': orderDetail.readyTime != null
          ? _formatTime(orderDetail.readyTime!)
          : null,
      'servedTime': orderDetail.servedTime != null
          ? _formatTime(orderDetail.servedTime!)
          : null,
      'bills': orderDetail.bills
          .map((bill) => {
                'billId': bill.billId,
                'billNumber': bill.billNumber,
                'subtotal': bill.subtotal,
                'tipAmount': bill.tipAmount,
                'totalTax': bill.totalTax,
                'totalDiscount': bill.totalDiscount,
                'serviceCharge': bill.serviceCharge,
                'deliveryCharge': bill.deliveryCharge,
                'packagingCharge': bill.packagingCharge,
                'totalAmount': bill.totalAmount,
                'status': bill.status,
                'taxBreakdown': bill.taxBreakdown,
                'discounts': bill.discounts,
                'payments': bill.payments,
                'customerInfo': bill.customerInfo != null
                    ? {
                        'name': bill.customerInfo!.name,
                        'phone': bill.customerInfo!.phoneNumber,
                        'email': bill.customerInfo!.email,
                      }
                    : null,
                'branchId': bill.branchId,
                'notes': bill.notes,
                'createdAt': bill.createdAt.toIso8601String(),
                'updatedAt': bill.updatedAt.toIso8601String(),
              })
          .toList(),
    };
  }

  /// Map API status to legacy status format
  static String _mapStatusToLegacy(String apiStatus) {
    switch (apiStatus.toUpperCase()) {
      case 'PENDING':
        return 'Pending';
      case 'IN_PREPARATION':
        return 'Cooking';
      case 'READY':
        return 'Ready';
      case 'SERVED':
        return 'Served';
      case 'COMPLETED':
        return 'Completed';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return apiStatus;
    }
  }

  /// Cancel/Delete order
  /// DELETE {{LAMBDA_HOST}}/order/delete/:orderDetailId
  static Future<ApiResult<void>> cancelOrder(String orderDetailId) async {
    try {
      final url = '$_baseUrl/order/delete/$orderDetailId';
      debugPrint('🍽️ OrderService: Canceling order at $url');

      final response = await HttpClientService.delete(url);
      debugPrint(
          '🍽️ OrderService: Cancel order response: ${response.statusCode}');
      debugPrint('🍽️ OrderService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        debugPrint('🍽️ OrderService: Successfully canceled order');
        return ApiResult.successNoData();
      } else {
        // Parse error response to get the specific error message
        String errorMessage = 'Failed to cancel order';
        try {
          if (response.body.isNotEmpty) {
            final responseData = json.decode(response.body);
            if (responseData is Map<String, dynamic>) {
              // Try to get message from different possible fields
              errorMessage = responseData['message'] ??
                  responseData['error'] ??
                  responseData['detail'] ??
                  errorMessage;
            }
          }
        } catch (parseError) {
          debugPrint('🍽️ OrderService: Error parsing response: $parseError');
          // Keep default error message if parsing fails
        }

        debugPrint('🍽️ OrderService: Failed to cancel order: $errorMessage');
        debugPrint(
            '🍽️ OrderService: Creating ApiResult.failure with message: $errorMessage');
        final result =
            ApiResult.failure(errorMessage, statusCode: response.statusCode);
        debugPrint(
            '🍽️ OrderService: ApiResult created - success: ${result.success}, errorMessage: ${result.errorMessage}');
        return result;
      }
    } catch (e) {
      debugPrint('🍽️ OrderService: Error canceling order: $e');
      return ApiResult.exception(Exception(e.toString()));
    }
  }

  /// Extract customization details from OrderItem
  static Map<String, dynamic>? _extractCustomization(OrderItem item) {
    final Map<String, dynamic> customization = {};

    if (item.allergies != null && item.allergies!.isNotEmpty) {
      customization['allergies'] = item.allergies!.map((allergy) => {
        'name': allergy.name,
        'id': allergy.id,
      }).toList();
    }

    if (item.spiciness != null && item.spiciness!.isNotEmpty) {
      customization['spiciness'] = item.spiciness;
    }

    if (item.dishAddons != null && item.dishAddons!.isNotEmpty) {
      customization['dishAddons'] = item.dishAddons!.map((addon) => {
        'name': addon.name,
        'quantity': addon.quantity,
        'price': addon.price,
      }).toList();
    }

    if (item.dishExtras != null && item.dishExtras!.isNotEmpty) {
      customization['dishExtras'] = item.dishExtras!.map((extra) => {
        'name': extra.name,
        'quantity': extra.quantity,
        'price': extra.price,
      }).toList();
    }

    if (item.notes != null && item.notes!.isNotEmpty) {
      customization['notes'] = item.notes;
    }

    return customization.isEmpty ? null : customization;
  }

  /// Format DateTime to time string
  static String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }
}
